{"productReview": {"title": "Product Reviews", "description": "View and manage all product reviews", "filters": {"rating": "Rating", "user": "User", "startDate": "Start Date", "endDate": "End Date", "selectUser": "Select user", "selectRating": "Select rating", "allRatings": "All Ratings", "stars": "Stars", "searchPlaceholder": "Search by user name or comment...", "sortBy": "Sort By", "sortByDate": "By Date", "sortByRating": "By Rating", "sortByUser": "By User", "sortDirection": "Sort Direction", "ascending": "Ascending", "descending": "Descending", "includeDeleted": "Show Deleted"}, "table": {"product": "Product", "user": "User", "rating": "Rating", "comment": "Comment", "date": "Date", "actions": "Actions", "noReviews": "No reviews found", "loadingReviews": "Loading reviews...", "deleted": "Deleted", "viewProduct": "View Product"}, "actions": {"delete": "Delete", "restore": "Rest<PERSON>", "view": "View", "approve": "Approve", "reject": "Reject"}, "messages": {"deleteSuccess": "Review deleted successfully", "deleteError": "Error deleting review", "restoreSuccess": "Review restored successfully", "restoreError": "Error restoring review", "deleteTitle": "Delete Review", "deleteConfirmation": "Are you sure you want to delete this review? This action can be undone.", "cancel": "Cancel", "delete": "Delete", "approveSuccess": "Review approved successfully", "approveError": "Error occurred while approving review", "rejectSuccess": "Review rejected successfully", "rejectError": "Error occurred while rejecting review"}, "pagination": {"previous": "Previous", "next": "Next", "page": "Page", "of": "of", "showing": "Showing"}, "stats": {"averageRating": "Average Rating", "totalReviews": "Total Reviews", "ratingDistribution": "Rating Distribution", "fiveStarReviews": "5-Star Reviews", "oneStarReviews": "1-Star Reviews"}}}