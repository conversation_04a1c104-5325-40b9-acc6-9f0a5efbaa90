import { redirect } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { hasServerPermission } from "@/lib/auth/server-permissions";
import PageHeaderServer from "../components/PageHeaderServer";
import { ProductReviewsListServer } from "./components/ProductReviewsListServer";

export default async function ProductReviewsPage() {
  // Server-side permission check - redirect if no access
  const canRead = await hasServerPermission("product", "read");
  if (!canRead) {
    redirect("/admin/dashboard");
  }

  const t = await getTranslations("productReview");
  const commonT = await getTranslations("common");

  return (
    <div className="space-y-6">
      <PageHeaderServer
        title={t("title")}
        description={t("description")}
        actions={[
          {
            actionLabel: commonT("actions.back"),
            actionVariant: "outline",
            actionUrl: "/admin/products",
            actionIcon: "back"
          }
        ]}
      />
      <ProductReviewsListServer />
    </div>
  );
}
