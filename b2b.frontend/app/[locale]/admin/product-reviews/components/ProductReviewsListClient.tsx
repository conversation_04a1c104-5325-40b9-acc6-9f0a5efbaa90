"use client"

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { useAllProductReviews, useDeleteProductReview, useRestoreProductReview, useApproveProductReview, useRejectProductReview } from '@/lib/api/hooks/useProductReviews';
import { useProductsDropdown } from '@/lib/api/hooks/useProducts';
import { ProductReview, ProductReviewFilter } from '@/types/product-review';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Star, Trash2, RotateCcw, Search, Check, X } from 'lucide-react';
import { format } from 'date-fns';
import { tr, enUS } from 'date-fns/locale';
import { useLocale } from 'next-intl';
import { toast } from 'sonner';
import Link from 'next/link';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

export function ProductReviewsListClient() {
  const t = useTranslations('productReview');
  const locale = useLocale();
  const dateLocale = locale === 'tr' ? tr : enUS;

  const [filter, setFilter] = useState<ProductReviewFilter>({
    pageNumber: 1,
    pageSize: 20,
    sortBy: 'CreatedAt',
    sortDirection: 'desc',
    includeDeleted: false,
  });

  const [searchTerm, setSearchTerm] = useState('');

  const { data: reviewsData, isLoading } = useAllProductReviews(filter);
  const { data: productsData } = useProductsDropdown();
  const deleteReviewMutation = useDeleteProductReview();
  const restoreReviewMutation = useRestoreProductReview();
  const approveReviewMutation = useApproveProductReview();
  const rejectReviewMutation = useRejectProductReview();

  const handleFilterChange = (key: keyof ProductReviewFilter, value: string | number | boolean | string[] | undefined) => {
    setFilter(prev => ({
      ...prev,
      [key]: value,
      pageNumber: key !== 'pageNumber' ? 1 : (typeof value === 'number' ? value : 1),
    }));
  };

  const handleDeleteReview = async (reviewId: string) => {
    try {
      await deleteReviewMutation.mutateAsync(reviewId);
      toast.success(t('messages.deleteSuccess'));
    } catch (_error) {
      toast.error(t('messages.deleteError'));
    }
  };

  const handleRestoreReview = async (reviewId: string) => {
    try {
      await restoreReviewMutation.mutateAsync(reviewId);
      toast.success(t('messages.restoreSuccess'));
    } catch (_error) {
      toast.error(t('messages.restoreError'));
    }
  };

  const handleApproveReview = async (reviewId: string) => {
    try {
      await approveReviewMutation.mutateAsync(reviewId);
      toast.success(t('messages.approveSuccess'));
    } catch (_error) {
      toast.error(t('messages.approveError'));
    }
  };

  const handleRejectReview = async (reviewId: string) => {
    try {
      await rejectReviewMutation.mutateAsync(reviewId);
      toast.success(t('messages.rejectSuccess'));
    } catch (_error) {
      toast.error(t('messages.rejectError'));
    }
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {Array.from({ length: 5 }).map((_, i) => (
          <Star
            key={i}
            className={`h-4 w-4 ${i < rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
              }`}
          />
        ))}
        <span className="ml-2 text-sm text-gray-600">{rating}</span>
      </div>
    );
  };

  if (isLoading) {
    return <div className="text-center py-8">{t('table.loadingReviews')}</div>;
  }

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('stats.totalReviews')}</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{reviewsData?.totalCount || 0}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('stats.averageRating')}</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {reviewsData?.reviews.length ?
                (reviewsData.reviews.reduce((acc, review) => acc + review.rating, 0) / reviewsData.reviews.length).toFixed(1)
                : '0.0'}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('stats.fiveStarReviews')}</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {reviewsData?.reviews.filter(r => r.rating === 5).length || 0}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('stats.oneStarReviews')}</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {reviewsData?.reviews.filter(r => r.rating === 1).length || 0}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder={t('filters.searchPlaceholder')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        <Select value={filter.rating?.toString() || 'all'} onValueChange={(value) => handleFilterChange('rating', value === 'all' ? undefined : parseInt(value))}>
          <SelectTrigger>
            <SelectValue placeholder={t('filters.rating')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t('filters.allRatings')}</SelectItem>
            <SelectItem value="5">5 {t('filters.stars')}</SelectItem>
            <SelectItem value="4">4 {t('filters.stars')}</SelectItem>
            <SelectItem value="3">3 {t('filters.stars')}</SelectItem>
            <SelectItem value="2">2 {t('filters.stars')}</SelectItem>
            <SelectItem value="1">1 {t('filters.stars')}</SelectItem>
          </SelectContent>
        </Select>

        {/* Ürün Filtresi */}
        <div className="relative">
          <Select>
            <SelectTrigger>
              <SelectValue placeholder={`Ürünler ${filter.productIds?.length ? `(${filter.productIds.length} seçili)` : ''}`} />
            </SelectTrigger>
            <SelectContent>
              <div className="p-2 space-y-2 max-h-60 overflow-y-auto">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="selectAllProducts"
                    checked={filter.productIds?.length === productsData?.length && productsData?.length > 0}
                    onChange={(e) => {
                      if (e.target.checked) {
                        handleFilterChange('productIds', productsData?.map(p => p.id) || []);
                      } else {
                        handleFilterChange('productIds', []);
                      }
                    }}
                  />
                  <label htmlFor="selectAllProducts" className="text-sm font-medium">
                    Tümünü Seç
                  </label>
                </div>
                {productsData?.map((product) => (
                  <div key={product.id} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={`product-${product.id}`}
                      checked={filter.productIds?.includes(product.id) || false}
                      onChange={(e) => {
                        const currentIds = filter.productIds || [];
                        if (e.target.checked) {
                          handleFilterChange('productIds', [...currentIds, product.id]);
                        } else {
                          handleFilterChange('productIds', currentIds.filter(id => id !== product.id));
                        }
                      }}
                    />
                    <label htmlFor={`product-${product.id}`} className="text-sm">
                      {product.name}
                    </label>
                  </div>
                ))}
              </div>
            </SelectContent>
          </Select>
        </div>

        <Select value={filter.sortBy || 'CreatedAt'} onValueChange={(value) => handleFilterChange('sortBy', value)}>
          <SelectTrigger>
            <SelectValue placeholder={t('filters.sortBy')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="CreatedAt">{t('filters.sortByDate')}</SelectItem>
            <SelectItem value="Rating">{t('filters.sortByRating')}</SelectItem>
            <SelectItem value="UserName">{t('filters.sortByUser')}</SelectItem>
          </SelectContent>
        </Select>

        <Select value={filter.sortDirection || 'desc'} onValueChange={(value) => handleFilterChange('sortDirection', value)}>
          <SelectTrigger>
            <SelectValue placeholder={t('filters.sortDirection')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="desc">{t('filters.descending')}</SelectItem>
            <SelectItem value="asc">{t('filters.ascending')}</SelectItem>
          </SelectContent>
        </Select>

        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="includeDeleted"
            checked={filter.includeDeleted || false}
            onChange={(e) => handleFilterChange('includeDeleted', e.target.checked)}
            className="rounded"
          />
          <label htmlFor="includeDeleted" className="text-sm">
            {t('filters.includeDeleted')}
          </label>
        </div>
      </div>

      {/* Reviews Table */}
      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t('table.product')}</TableHead>
              <TableHead>{t('table.user')}</TableHead>
              <TableHead>{t('table.rating')}</TableHead>
              <TableHead>{t('table.comment')}</TableHead>
              <TableHead>{t('table.date')}</TableHead>
              <TableHead className="text-right">{t('table.actions')}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {reviewsData?.reviews.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8 text-gray-500">
                  {t('table.noReviews')}
                </TableCell>
              </TableRow>
            ) : (
              reviewsData?.reviews
                .filter(review =>
                  !searchTerm ||
                  review.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                  review.comment.toLowerCase().includes(searchTerm.toLowerCase())
                )
                .map((review: ProductReview) => (
                  <TableRow key={review.id} className={review.isDeleted ? 'opacity-50' : ''}>
                    <TableCell>
                      <Link
                        href={`/admin/products/edit/${review.productId}`}
                        className="text-blue-600 hover:text-blue-800 hover:underline"
                      >
                        {review.productName}
                      </Link>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{review.userName}</div>
                        <div className="text-sm text-gray-500">{review.userEmail}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {renderStars(review.rating)}
                    </TableCell>
                    <TableCell>
                      <div className="max-w-xs truncate" title={review.comment}>
                        {review.comment}
                      </div>
                      <div className="mt-1">
                        <Badge
                          variant={review.isApproved ? "default" : "secondary"}
                          className={review.isApproved ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800"}
                        >
                          {review.isApproved ? "Onaylı" : "Beklemede"}
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {format(new Date(review.createdAt), 'dd MMM yyyy', { locale: dateLocale })}
                      </div>
                      <div className="text-xs text-gray-500">
                        {format(new Date(review.createdAt), 'HH:mm')}
                      </div>
                      {review.isDeleted && review.deletedAt && (
                        <Badge variant="destructive" className="mt-1">
                          {t('table.deleted')}
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        {review.isDeleted ? (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleRestoreReview(review.id)}
                            disabled={restoreReviewMutation.isPending}
                          >
                            <RotateCcw className="h-4 w-4" />
                          </Button>
                        ) : (
                          <>
                            {/* Onay/Red Butonları */}
                            {review.isApproved ? (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleRejectReview(review.id)}
                                disabled={rejectReviewMutation.isPending}
                                className="text-red-600 hover:text-red-700"
                                title={t('actions.reject')}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            ) : (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleApproveReview(review.id)}
                                disabled={approveReviewMutation.isPending}
                                className="text-green-600 hover:text-green-700"
                                title={t('actions.approve')}
                              >
                                <Check className="h-4 w-4" />
                              </Button>
                            )}

                            {/* Silme Butonu */}
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  disabled={deleteReviewMutation.isPending}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>{t('messages.deleteTitle')}</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    {t('messages.deleteConfirmation')}
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>{t('messages.cancel')}</AlertDialogCancel>
                                  <AlertDialogAction onClick={() => handleDeleteReview(review.id)}>
                                    {t('messages.delete')}
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {reviewsData && reviewsData.totalPages > 1 && (
        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-500">
            {t('pagination.showing')} {((filter.pageNumber || 1) - 1) * (filter.pageSize || 20) + 1} - {Math.min((filter.pageNumber || 1) * (filter.pageSize || 20), reviewsData.totalCount)} {t('pagination.of')} {reviewsData.totalCount}
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleFilterChange('pageNumber', (filter.pageNumber || 1) - 1)}
              disabled={!reviewsData.hasPreviousPage}
            >
              {t('pagination.previous')}
            </Button>
            <span className="flex items-center px-3 py-1 text-sm">
              {filter.pageNumber || 1} / {reviewsData.totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleFilterChange('pageNumber', (filter.pageNumber || 1) + 1)}
              disabled={!reviewsData.hasNextPage}
            >
              {t('pagination.next')}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
