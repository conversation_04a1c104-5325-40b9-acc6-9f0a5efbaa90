'use client';

import { useState } from 'react';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useShippingLabelByOrderId } from '@/lib/api/hooks/useShippingLabel';
import { Printer, X, Loader2 } from 'lucide-react';
import { toast } from 'sonner';

interface ShippingLabelModalProps {
  isOpen: boolean;
  onClose: () => void;
  orderId: string;
}

export default function ShippingLabelModal({
  isOpen,
  onClose,
  orderId,
}: ShippingLabelModalProps) {
  const t = useTranslations('order');
  const [isPrinting, setIsPrinting] = useState(false);

  const { data: shippingLabel, isLoading, error } = useShippingLabelByOrderId(orderId);

  const handlePrint = async () => {
    try {
      setIsPrinting(true);

      // Yazdırma için özel CSS ile yeni bir pencere aç
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        toast.error('Popup engelleyici nedeniyle yazdırma penceresi açılamadı');
        return;
      }

      const printContent = generatePrintContent();

      printWindow.document.write(printContent);
      printWindow.document.close();

      // Resimler yüklendikten sonra yazdır
      printWindow.onload = () => {
        setTimeout(() => {
          printWindow.print();
          printWindow.close();
        }, 500);
      };

      toast.success('Kargo etiketi yazdırılıyor...');
    } catch (error) {
      console.error('Print error:', error);
      toast.error('Yazdırma sırasında hata oluştu');
    } finally {
      setIsPrinting(false);
    }
  };

  const generatePrintContent = () => {
    if (!shippingLabel) return '';

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Kargo Etiketi - ${shippingLabel.orderNumber}</title>
          <style>
            @page {
              size: 10cm 10cm;
              margin: 0;
            }
            * {
              margin: 0;
              padding: 0;
              box-sizing: border-box;
            }
            body {
              font-family: Arial, sans-serif;
              font-size: 8px;
              line-height: 1.2;
              width: 10cm;
              height: 10cm;
              padding: 0.2cm;
              background: white;
            }
            .header {
              text-align: center;
              margin-bottom: 0.2cm;
            }
            .logo {
              max-width: 2cm;
              max-height: 1cm;
              margin-bottom: 0.1cm;
            }
            .company-name {
              font-weight: bold;
              font-size: 10px;
              margin-bottom: 0.2cm;
            }
            .barcode-section {
              text-align: center;
              margin-bottom: 0.2cm;
            }
            .barcode {
              max-width: 8cm;
              height: auto;
              margin-bottom: 0.1cm;
            }
            .cargo-code {
              font-weight: bold;
              font-size: 9px;
            }
            .info-section {
              margin-bottom: 0.15cm;
            }
            .info-row {
              display: flex;
              margin-bottom: 0.05cm;
            }
            .info-label {
              font-weight: bold;
              width: 2.5cm;
              flex-shrink: 0;
            }
            .info-value {
              flex: 1;
              word-wrap: break-word;
            }
            .separator {
              border-top: 1px solid #ccc;
              margin: 0.1cm 0;
            }
            .address-section {
              border: 1px solid #000;
              padding: 0.1cm;
              margin-top: 0.1cm;
            }
            .address-title {
              font-weight: bold;
              text-align: center;
              margin-bottom: 0.1cm;
              font-size: 9px;
            }
          </style>
        </head>
        <body>
          <div class="header">
            ${shippingLabel.companyLogoUrl ? `<img src="${shippingLabel.companyLogoUrl}" alt="Logo" class="logo" />` : ''}
            <div class="company-name">${shippingLabel.companyName}</div>
          </div>
          
          <div class="barcode-section">
            <img src="data:image/png;base64,${shippingLabel.barcodeImage}" alt="Barkod" class="barcode" />
            <div class="cargo-code">${shippingLabel.cargoCode}</div>
          </div>
          
          <div class="info-section">
            <div class="info-row">
              <span class="info-label">Sipariş No:</span>
              <span class="info-value">${shippingLabel.orderNumber}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Gönderen:</span>
              <span class="info-value">${shippingLabel.sender.name}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Satış Kanalı:</span>
              <span class="info-value">${shippingLabel.salesChannel}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Alıcı:</span>
              <span class="info-value">${shippingLabel.recipient.name} / ${shippingLabel.recipient.phone}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Kargo Şirketi:</span>
              <span class="info-value">${shippingLabel.carrierName}</span>
            </div>
          </div>
          
          <div class="address-section">
            <div class="address-title">ADRES</div>
            <div>${shippingLabel.deliveryAddress}</div>
          </div>
        </body>
      </html>
    `;
  };

  if (error) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              Kargo Etiketi
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            </DialogTitle>
          </DialogHeader>
          <div className="text-center py-8">
            <p className="text-red-600">Kargo etiketi yüklenirken hata oluştu</p>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            Kargo Etiketi
            <div className="flex items-center gap-2 mr-8">
              <Button
                onClick={handlePrint}
                disabled={isLoading || !shippingLabel || isPrinting}
                size="sm"
                className="flex items-center gap-2"
              >
                {isPrinting ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Printer className="h-4 w-4" />
                )}
                Yazdır
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Kargo etiketi yükleniyor...</span>
          </div>
        ) : shippingLabel ? (
          <Card className="w-full max-w-md mx-auto">
            <CardContent className="p-4 space-y-4">
              {/* Header - Logo ve Şirket Adı */}
              <div className="text-center space-y-2">
                {shippingLabel.companyLogoUrl && (
                  <div className="flex justify-center">
                    <Image
                      src={shippingLabel.companyLogoUrl}
                      alt="Şirket Logosu"
                      width={80}
                      height={40}
                      className="object-contain"
                    />
                  </div>
                )}
                <h3 className="font-bold text-lg">{shippingLabel.companyName}</h3>
              </div>

              <Separator />

              {/* Barkod */}
              <div className="text-center space-y-2">
                <img
                  src={`data:image/png;base64,${shippingLabel.barcodeImage}`}
                  alt="Kargo Barkodu"
                  width={250}
                  height={80}
                  className="mx-auto"
                />
                <p className="font-mono font-bold text-sm">{shippingLabel.cargoCode}</p>
              </div>

              <Separator />

              {/* Sipariş Bilgileri */}
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="font-semibold">Sipariş No:</span>
                  <span>{shippingLabel.orderNumber}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-semibold">Gönderen:</span>
                  <span>{shippingLabel.sender.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-semibold">Satış Kanalı:</span>
                  <span>{shippingLabel.salesChannel}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-semibold">Alıcı:</span>
                  <span>{shippingLabel.recipient.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-semibold">Telefon:</span>
                  <span>{shippingLabel.recipient.phone}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-semibold">Kargo Şirketi:</span>
                  <span>{shippingLabel.carrierName}</span>
                </div>
              </div>

              <Separator />

              {/* Adres */}
              <div className="border border-gray-300 p-3 rounded">
                <h4 className="font-bold text-center mb-2">ADRES</h4>
                <p className="text-sm text-center">{shippingLabel.deliveryAddress}</p>
              </div>
            </CardContent>
          </Card>
        ) : null}
      </DialogContent>
    </Dialog>
  );
}
