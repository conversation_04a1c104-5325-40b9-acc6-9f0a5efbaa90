'use client';

import { useQuery } from '@tanstack/react-query';
import { api } from '../client';

// Types
export interface SenderInfo {
  name: string;
  phone?: string;
  address?: string;
}

export interface RecipientInfo {
  name: string;
  phone: string;
  address: string;
}

export interface ShippingLabel {
  companyLogoUrl?: string;
  companyName: string;
  cargoCode: string;
  barcodeImage: string;
  orderNumber: string;
  sender: SenderInfo;
  salesChannel: string;
  recipient: RecipientInfo;
  carrierName: string;
  deliveryAddress: string;
}

// API functions
const shippingLabelApi = {
  getByOrderId: async (orderId: string): Promise<ShippingLabel> => {
    const response = await api.get(`/ShippingLabel/order/${orderId}`);
    return response;
  },

  getByShipmentId: async (shipmentId: string): Promise<ShippingLabel> => {
    const response = await api.get(`/ShippingLabel/shipment/${shipmentId}`);
    return response;
  },
};

// Hooks
export const useShippingLabelByOrderId = (orderId: string) => {
  return useQuery({
    queryKey: ['shipping-label', 'order', orderId],
    queryFn: () => shippingLabelApi.getByOrderId(orderId),
    enabled: !!orderId,
  });
};

export const useShippingLabelByShipmentId = (shipmentId: string) => {
  return useQuery({
    queryKey: ['shipping-label', 'shipment', shipmentId],
    queryFn: () => shippingLabelApi.getByShipmentId(shipmentId),
    enabled: !!shipmentId,
  });
};
