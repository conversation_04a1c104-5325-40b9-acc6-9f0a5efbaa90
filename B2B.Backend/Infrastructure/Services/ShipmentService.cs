using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Core.Entities;
using Core.Enums;
using Core.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Services;

public class ShipmentService : IShipmentService
{
    private readonly IGenericRepository<Shipment> _shipmentRepository;
    private readonly IGenericRepository<Order> _orderRepository;
    private readonly ICurrentUserService _currentUserService;

    public ShipmentService(
        IGenericRepository<Shipment> shipmentRepository,
        IGenericRepository<Order> orderRepository,
        ICurrentUserService currentUserService)
    {
        _shipmentRepository = shipmentRepository;
        _orderRepository = orderRepository;
        _currentUserService = currentUserService;
    }

    public async Task<IEnumerable<ShipmentDto>> GetAllAsync()
    {
        var shipments = await _shipmentRepository.Query()
            .Include(s => s.Order)
                .ThenInclude(o => o.Customer)
            .Include(s => s.Carrier)
            .Where(s => !s.IsDeleted)
            .OrderByDescending(s => s.CreatedAt)
            .ToListAsync();

        return shipments.Select(s => new ShipmentDto
        {
            Id = s.Id,
            OrderId = s.OrderId,
            TrackingNumber = s.TrackingNumber,
            CarrierId = s.CarrierId,
            CarrierName = s.Carrier.Name,
            CarrierShortCode = s.Carrier.ShortCode,
            Status = s.Status,
            ShippedAt = s.ShippedAt,
            DeliveredAt = s.DeliveredAt,
            Notes = s.Notes,
            CreatedAt = s.CreatedAt
        });
    }

    public async Task<ShipmentDto?> GetByIdAsync(Guid id)
    {
        var shipment = await _shipmentRepository.Query()
            .Include(s => s.Order)
                .ThenInclude(o => o.Customer)
            .Include(s => s.Carrier)
            .FirstOrDefaultAsync(s => s.Id == id && !s.IsDeleted);

        if (shipment == null) return null;

        return new ShipmentDto
        {
            Id = shipment.Id,
            OrderId = shipment.OrderId,
            TrackingNumber = shipment.TrackingNumber,
            CarrierId = shipment.CarrierId,
            CarrierName = shipment.Carrier.Name,
            CarrierShortCode = shipment.Carrier.ShortCode,
            Status = shipment.Status,
            ShippedAt = shipment.ShippedAt,
            DeliveredAt = shipment.DeliveredAt,
            Notes = shipment.Notes,
            CreatedAt = shipment.CreatedAt
        };
    }

    public async Task<IEnumerable<ShipmentDto>> GetByOrderIdAsync(Guid orderId)
    {
        var shipments = await _shipmentRepository.Query()
            .Include(s => s.Order)
                .ThenInclude(o => o.Customer)
            .Include(s => s.Carrier)
            .Where(s => s.OrderId == orderId && !s.IsDeleted)
            .OrderByDescending(s => s.CreatedAt)
            .ToListAsync();

        return shipments.Select(s => new ShipmentDto
        {
            Id = s.Id,
            OrderId = s.OrderId,
            CargoKey = s.CargoKey,
            TrackingNumber = s.TrackingNumber,
            CarrierId = s.CarrierId,
            CarrierName = s.Carrier.Name,
            CarrierShortCode = s.Carrier.ShortCode,
            Status = s.Status,
            ShippedAt = s.ShippedAt,
            DeliveredAt = s.DeliveredAt,
            Notes = s.Notes,
            CreatedAt = s.CreatedAt
        });
    }

    public async Task<IEnumerable<ShipmentDto>> GetByStatusAsync(ShipmentStatus status)
    {
        var shipments = await _shipmentRepository.Query()
            .Include(s => s.Order)
                .ThenInclude(o => o.Customer)
            .Include(s => s.Carrier)
            .Where(s => s.Status == status && !s.IsDeleted)
            .OrderByDescending(s => s.CreatedAt)
            .ToListAsync();

        return shipments.Select(s => new ShipmentDto
        {
            Id = s.Id,
            OrderId = s.OrderId,
            TrackingNumber = s.TrackingNumber,
            CarrierId = s.CarrierId,
            CarrierName = s.Carrier.Name,
            CarrierShortCode = s.Carrier.ShortCode,
            Status = s.Status,
            ShippedAt = s.ShippedAt,
            DeliveredAt = s.DeliveredAt,
            Notes = s.Notes,
            CreatedAt = s.CreatedAt
        });
    }

    public async Task<IEnumerable<ShipmentDto>> SearchAsync(string searchTerm)
    {
        var shipments = await _shipmentRepository.Query()
            .Include(s => s.Order)
                .ThenInclude(o => o.Customer)
            .Include(s => s.Carrier)
            .Where(s => !s.IsDeleted &&
                       (s.TrackingNumber.Contains(searchTerm) ||
                        s.Carrier.Name.Contains(searchTerm) ||
                        s.Carrier.ShortCode.Contains(searchTerm) ||
                        s.Order.OrderNumber.Contains(searchTerm) ||
                        s.Order.Customer.NameSurname.Contains(searchTerm)))
            .OrderByDescending(s => s.CreatedAt)
            .ToListAsync();

        return shipments.Select(s => new ShipmentDto
        {
            Id = s.Id,
            OrderId = s.OrderId,
            TrackingNumber = s.TrackingNumber,
            CarrierId = s.CarrierId,
            CarrierName = s.Carrier.Name,
            CarrierShortCode = s.Carrier.ShortCode,
            Status = s.Status,
            ShippedAt = s.ShippedAt,
            DeliveredAt = s.DeliveredAt,
            Notes = s.Notes,
            CreatedAt = s.CreatedAt
        });
    }

    public async Task<ShipmentDto> CreateAsync(CreateShipmentDto dto)
    {
        // Verify order exists
        var order = await _orderRepository.GetByIdAsync(dto.OrderId);
        if (order == null || order.IsDeleted)
            throw new ArgumentException("Order not found");

        var shipment = new Shipment
        {
            Id = Guid.CreateVersion7(),
            OrderId = dto.OrderId,
            TrackingNumber = dto.TrackingNumber,
            CargoKey = dto.CargoKey,
            CarrierId = dto.CarrierId,
            Status = dto.Status,
            ShippedAt = dto.ShippedAt,
            DeliveredAt = dto.DeliveredAt,
            Notes = dto.Notes,
            EmployeeId = _currentUserService.UserId, // Mevcut kullanıcıyı set et
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await _shipmentRepository.AddAsync(shipment);
        await _shipmentRepository.SaveChangesAsync(); // SaveChanges çağrısını ekle

        // Carrier bilgisini almak için tekrar query yapalım
        var createdShipment = await _shipmentRepository.Query()
            .Include(s => s.Carrier)
            .FirstOrDefaultAsync(s => s.Id == shipment.Id);

        return new ShipmentDto
        {
            Id = shipment.Id,
            OrderId = shipment.OrderId,
            TrackingNumber = shipment.TrackingNumber,
            CargoKey = shipment.CargoKey,
            CarrierId = shipment.CarrierId,
            CarrierName = createdShipment!.Carrier.Name,
            CarrierShortCode = createdShipment.Carrier.ShortCode,
            Status = shipment.Status,
            ShippedAt = shipment.ShippedAt,
            DeliveredAt = shipment.DeliveredAt,
            Notes = shipment.Notes,
            CreatedAt = shipment.CreatedAt
        };
    }

    public async Task<ShipmentDto> UpdateAsync(Guid id, UpdateShipmentDto dto)
    {
        var shipment = await _shipmentRepository.GetByIdAsync(id);
        if (shipment == null || shipment.IsDeleted)
            throw new ArgumentException("Shipment not found");

        if (!string.IsNullOrEmpty(dto.TrackingNumber))
            shipment.TrackingNumber = dto.TrackingNumber;
        if (!string.IsNullOrEmpty(dto.CargoKey))
            shipment.CargoKey = dto.CargoKey;
        if (dto.CarrierId.HasValue)
            shipment.CarrierId = dto.CarrierId.Value;
        if (dto.Status.HasValue)
            shipment.Status = dto.Status.Value;
        if (dto.ShippedAt.HasValue)
            shipment.ShippedAt = dto.ShippedAt.Value;
        if (dto.DeliveredAt.HasValue)
            shipment.DeliveredAt = dto.DeliveredAt.Value;
        if (dto.Notes != null)
            shipment.Notes = dto.Notes;

        shipment.UpdatedAt = DateTime.UtcNow;

        _shipmentRepository.Update(shipment);
        await _shipmentRepository.SaveChangesAsync();

        // Güncellenmiş shipment'ı Carrier bilgisi ile birlikte al
        var updatedShipment = await _shipmentRepository.Query()
            .Include(s => s.Carrier)
            .FirstOrDefaultAsync(s => s.Id == id);

        return new ShipmentDto
        {
            Id = updatedShipment!.Id,
            OrderId = updatedShipment.OrderId,
            TrackingNumber = updatedShipment.TrackingNumber,
            CargoKey = updatedShipment.CargoKey,
            CarrierId = updatedShipment.CarrierId,
            CarrierName = updatedShipment.Carrier.Name,
            CarrierShortCode = updatedShipment.Carrier.ShortCode,
            Status = updatedShipment.Status,
            ShippedAt = updatedShipment.ShippedAt,
            DeliveredAt = updatedShipment.DeliveredAt,
            Notes = updatedShipment.Notes,
            CreatedAt = updatedShipment.CreatedAt
        };
    }

    public async Task<bool> DeleteAsync(Guid id)
    {
        var shipment = await _shipmentRepository.GetByIdAsync(id);
        if (shipment == null || shipment.IsDeleted)
            return false;

        shipment.IsDeleted = true;
        shipment.UpdatedAt = DateTime.UtcNow;

        _shipmentRepository.Update(shipment);
        await _shipmentRepository.SaveChangesAsync();
        return true;
    }

    public async Task<bool> UpdateStatusAsync(Guid id, ShipmentStatus status)
    {
        var shipment = await _shipmentRepository.GetByIdAsync(id);
        if (shipment == null || shipment.IsDeleted)
            return false;

        shipment.Status = status;
        if (status == ShipmentStatus.Delivered && !shipment.DeliveredAt.HasValue)
            shipment.DeliveredAt = DateTime.UtcNow;
        
        shipment.UpdatedAt = DateTime.UtcNow;

        _shipmentRepository.Update(shipment);
        await _shipmentRepository.SaveChangesAsync();
        return true;
    }

    public async Task<ShipmentDto?> GetByTrackingNumberAsync(string trackingNumber)
    {
        var shipment = await _shipmentRepository.Query()
            .Include(s => s.Order)
                .ThenInclude(o => o.Customer)
            .Include(s => s.Carrier)
            .FirstOrDefaultAsync(s => s.TrackingNumber == trackingNumber && !s.IsDeleted);

        if (shipment == null) return null;

        return new ShipmentDto
        {
            Id = shipment.Id,
            OrderId = shipment.OrderId,
            TrackingNumber = shipment.TrackingNumber,
            CarrierId = shipment.CarrierId,
            CarrierName = shipment.Carrier.Name,
            CarrierShortCode = shipment.Carrier.ShortCode,
            Status = shipment.Status,
            ShippedAt = shipment.ShippedAt,
            DeliveredAt = shipment.DeliveredAt,
            Notes = shipment.Notes,
            CreatedAt = shipment.CreatedAt
        };
    }
}
