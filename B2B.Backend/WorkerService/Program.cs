using WorkerService.Services;
using WorkerService.Extensions;
using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Infrastructure.Services;

var builder = Host.CreateApplicationBuilder(args);

// Environment variables configuration
builder.Configuration.AddEnvironmentVariables();

// Database configuration
var connectionString = builder.Configuration.GetConnectionString("DefaultConnection") ??
                      Environment.GetEnvironmentVariable("DATABASE_URL") ??
                      throw new InvalidOperationException("Connection string 'DefaultConnection' not found.");

builder.Services.AddDbContext<B2BDbContext>(options =>
    options.UseNpgsql(connectionString));

// Infrastructure services
builder.Services.AddInfrastructure();

// Shipping modules
builder.Services.AddModules();

// MassTransit konfigürasyonu
builder.Services.AddMassTransitConfiguration(builder.Configuration);

// Worker services
builder.Services.AddHostedService<ShipmentTrackingWorker>();
// Logging
builder.Services.AddLogging(logging =>
{
    logging.AddConsole();
    logging.AddDebug();
});

var host = builder.Build();

// Ensure database is created and migrated
using (var scope = host.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<B2BDbContext>();
    await context.Database.MigrateAsync();
}

host.Run();
