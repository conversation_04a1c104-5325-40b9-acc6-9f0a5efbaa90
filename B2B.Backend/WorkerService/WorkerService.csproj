<Project Sdk="Microsoft.NET.Sdk.Worker">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>dotnet-WorkerService-20241208-1234</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting.WindowsServices" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.4" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
    <PackageReference Include="MassTransit" Version="8.5.2" />
    <PackageReference Include="MassTransit.RabbitMQ" Version="8.5.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Core\Core.csproj" />
    <ProjectReference Include="..\Infrastructure\Infrastructure.csproj" />
    <ProjectReference Include="..\Application.Contracts\Application.Contracts.csproj" />
    <ProjectReference Include="..\Modules\Shipping.Abstraction\Shipping.Abstraction.csproj" />
    <ProjectReference Include="..\Modules\Shipping.Implementation\Shipping.Implementation.csproj" />
    <ProjectReference Include="..\Modules\Shipping.YurticiKargo\Shipping.YurticiKargo.csproj" />
    <ProjectReference
      Include="..\Modules\Shipping.YurticiKargoTest\Shipping.YurticiKargoTest.csproj" />
  </ItemGroup>

</Project>