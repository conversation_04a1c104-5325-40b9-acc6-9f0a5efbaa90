using MassTransit;
using Core.Events;

namespace WorkerService.Extensions;

public static class MassTransitExtensions
{
    public static IServiceCollection AddMassTransitConfiguration(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddMassTransit(x =>
        {
            x.UsingRabbitMq((context, cfg) =>
            {
                // MediaAPI ile aynı format kullan - appsettings.json'dan oku
                var host = configuration.GetValue<string>("RabbitMQ:Host") ?? "localhost";
                var username = configuration.GetValue<string>("RabbitMQ:Username") ?? "guest";
                var password = configuration.GetValue<string>("RabbitMQ:Password") ?? "guest";

                cfg.Host(host, h =>
                {
                    h.Username(username);
                    h.Password(password);
                });

                // Shipment tracking notification event'i için exchange konfigürasyonu
                cfg.Message<CustomerShipmentNotificationRequested>(e => e.SetEntityName("customer-shipment-notification"));
                cfg.Publish<CustomerShipmentNotificationRequested>(e => e.ExchangeType = "fanout");
            });
        });

        return services;
    }
}
