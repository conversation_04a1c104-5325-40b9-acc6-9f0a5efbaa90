using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Application.Contracts.Interfaces;
using Core.Interfaces;
using Core.Entities;
using PanelApi.DTOs;
using PanelApi.Services;
using PanelApi.Attributes;

namespace PanelApi.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class ShippingLabelController : ControllerBase
{
    private readonly IShipmentService _shipmentService;
    private readonly IOrderService _orderService;
    private readonly IGenericRepository<CompanyInfo> _companyInfoRepository;
    private readonly IGenericRepository<ShippingCarrier> _carrierRepository;
    private readonly IBarcodeService _barcodeService;
    private readonly ILogger<ShippingLabelController> _logger;

    public ShippingLabelController(
        IShipmentService shipmentService,
        IOrderService orderService,
        IGenericRepository<CompanyInfo> companyInfoRepository,
        IGenericRepository<ShippingCarrier> carrierRepository,
        IBarcodeService barcodeService,
        ILogger<ShippingLabelController> logger)
    {
        _shipmentService = shipmentService;
        _orderService = orderService;
        _companyInfoRepository = companyInfoRepository;
        _carrierRepository = carrierRepository;
        _barcodeService = barcodeService;
        _logger = logger;
    }

    /// <summary>
    /// Sipariş ID'sine göre kargo etiketi bilgilerini getirir
    /// </summary>
    /// <param name="orderId">Sipariş ID'si</param>
    /// <returns>Kargo etiketi bilgileri</returns>
    [HttpGet("order/{orderId}")]
    [RequirePermission("order", "read")]
    public async Task<ActionResult<ShippingLabelDto>> GetShippingLabelByOrderId(Guid orderId)
    {
        try
        {
            _logger.LogInformation("Getting shipping label for order: {OrderId}", orderId);

            // Siparişi getir
            var order = await _orderService.GetByIdAsync(orderId);
            if (order == null)
            {
                return NotFound(new { message = "Sipariş bulunamadı" });
            }

            // Sipariş için shipment'ları getir
            var shipments = await _shipmentService.GetByOrderIdAsync(orderId);
            var activeShipment = shipments.FirstOrDefault(s => s.Status != Core.Enums.ShipmentStatus.Cancelled);
            
            if (activeShipment == null)
            {
                return BadRequest(new { message = "Bu sipariş için aktif kargo kaydı bulunamadı" });
            }

            // Şirket bilgilerini getir
            var companyInfo = await _companyInfoRepository.Query().FirstOrDefaultAsync();
            
            // Kargo firması bilgilerini getir
            var carrier = await _carrierRepository.GetByIdAsync(activeShipment.CarrierId);
            if (carrier == null)
            {
                return BadRequest(new { message = "Kargo firması bilgisi bulunamadı" });
            }

            // Barkod oluştur
            var barcodeImage = _barcodeService.GenerateBarcode(activeShipment.CargoKey);

            // Kargo etiketi DTO'sunu oluştur
            var shippingLabel = new ShippingLabelDto
            {
                CompanyLogoUrl = companyInfo?.LogoUrl,
                CompanyName = companyInfo?.CompanyName ?? "Şirket Adı",
                CargoCode = activeShipment.CargoKey,
                BarcodeImage = barcodeImage,
                OrderNumber = order.OrderNumber,
                Sender = new SenderInfoDto
                {
                    Name = companyInfo?.CompanyName ?? "Şirket Adı",
                    Phone = companyInfo?.PhoneNumber,
                    Address = companyInfo?.Address
                },
                SalesChannel = "B2B Portal", // Bu değer ayarlardan gelebilir
                Recipient = new RecipientInfoDto
                {
                    Name = order.Customer?.NameSurname ?? "Müşteri",
                    Phone = order.Customer?.PhoneNumber ?? "",
                    Address = order.Address != null
                        ? $"{order.Address.Line1} {order.Address.Line2}, {order.Address.District}, {order.Address.City}"
                        : "Adres bilgisi yok"
                },
                CarrierName = carrier.Name,
                DeliveryAddress = order.Address != null
                    ? $"{order.Address.Line1} {order.Address.Line2}, {order.Address.District}, {order.Address.City}"
                    : "Adres bilgisi yok"
            };

            _logger.LogInformation("Shipping label generated successfully for order: {OrderId}", orderId);
            return Ok(shippingLabel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating shipping label for order: {OrderId}", orderId);
            return StatusCode(500, new { message = "Kargo etiketi oluşturulurken hata oluştu" });
        }
    }

    /// <summary>
    /// Shipment ID'sine göre kargo etiketi bilgilerini getirir
    /// </summary>
    /// <param name="shipmentId">Shipment ID'si</param>
    /// <returns>Kargo etiketi bilgileri</returns>
    [HttpGet("shipment/{shipmentId}")]
    [RequirePermission("order", "read")]
    public async Task<ActionResult<ShippingLabelDto>> GetShippingLabelByShipmentId(Guid shipmentId)
    {
        try
        {
            _logger.LogInformation("Getting shipping label for shipment: {ShipmentId}", shipmentId);

            // Shipment'ı getir
            var shipment = await _shipmentService.GetByIdAsync(shipmentId);
            if (shipment == null)
            {
                return NotFound(new { message = "Kargo kaydı bulunamadı" });
            }

            // Siparişi getir
            var order = await _orderService.GetByIdAsync(shipment.OrderId);
            if (order == null)
            {
                return NotFound(new { message = "Sipariş bulunamadı" });
            }

            // Şirket bilgilerini getir
            var companyInfo = await _companyInfoRepository.Query().FirstOrDefaultAsync();
            
            // Kargo firması bilgilerini getir
            var carrier = await _carrierRepository.GetByIdAsync(shipment.CarrierId);
            if (carrier == null)
            {
                return BadRequest(new { message = "Kargo firması bilgisi bulunamadı" });
            }

            // Barkod oluştur
            var barcodeImage = _barcodeService.GenerateBarcode(shipment.CargoKey);

            // Kargo etiketi DTO'sunu oluştur
            var shippingLabel = new ShippingLabelDto
            {
                CompanyLogoUrl = companyInfo?.LogoUrl,
                CompanyName = companyInfo?.CompanyName ?? "Şirket Adı",
                CargoCode = shipment.CargoKey,
                BarcodeImage = barcodeImage,
                OrderNumber = order.OrderNumber,
                Sender = new SenderInfoDto
                {
                    Name = companyInfo?.CompanyName ?? "Şirket Adı",
                    Phone = companyInfo?.PhoneNumber,
                    Address = companyInfo?.Address
                },
                SalesChannel = "Future Cosmetics", // Bu değer ayarlardan gelebilir
                Recipient = new RecipientInfoDto
                {
                    Name = order.Customer?.NameSurname ?? "Müşteri",
                    Phone = order.Customer?.PhoneNumber ?? "",
                    Address = order.Address != null
                        ? $"{order.Address.Line1} {order.Address.Line2}, {order.Address.District}, {order.Address.City}"
                        : "Adres bilgisi yok"
                },
                CarrierName = carrier.Name,
                DeliveryAddress = order.Address != null
                    ? $"{order.Address.Line1} {order.Address.Line2}, {order.Address.District}, {order.Address.City}"
                    : "Adres bilgisi yok"
            };

            _logger.LogInformation("Shipping label generated successfully for shipment: {ShipmentId}", shipmentId);
            return Ok(shippingLabel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating shipping label for shipment: {ShipmentId}", shipmentId);
            return StatusCode(500, new { message = "Kargo etiketi oluşturulurken hata oluştu" });
        }
    }
}
