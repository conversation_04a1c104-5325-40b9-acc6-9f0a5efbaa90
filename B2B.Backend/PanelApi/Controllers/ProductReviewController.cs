using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PanelApi.Attributes;

namespace PanelApi.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class ProductReviewController : ControllerBase
{
    private readonly IProductReviewService _productReviewService;

    public ProductReviewController(IProductReviewService productReviewService)
    {
        _productReviewService = productReviewService;
    }

    // Tüm yorumları getir (admin panel için)
    [HttpGet]
    [RequirePermission("product", "read")]
    public async Task<ActionResult<ProductReviewListDto>> GetAllReviews(
        [FromQuery] int? rating,
        [FromQuery] Guid? userId,
        [FromQuery] List<Guid>? productIds,
        [FromQuery] DateTime? startDate,
        [FromQuery] DateTime? endDate,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 20,
        [FromQuery] string? sortBy = "CreatedAt",
        [FromQuery] string? sortDirection = "desc",
        [FromQuery] bool includeDeleted = false)
    {
        try
        {
            var filter = new ProductReviewFilterDto
            {
                Rating = rating,
                UserId = userId,
                ProductIds = productIds,
                StartDate = startDate,
                EndDate = endDate,
                PageNumber = pageNumber,
                PageSize = pageSize,
                SortBy = sortBy,
                SortDirection = sortDirection,
                IncludeDeleted = includeDeleted
            };

            var reviews = await _productReviewService.GetAllAsync(filter);
            return Ok(reviews);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Yorumlar getirilirken bir hata oluştu.", error = ex.Message });
        }
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<ProductReviewDto>> GetById(Guid id)
    {
        try
        {
            var review = await _productReviewService.GetByIdAsync(id);
            if (review == null)
            {
                return NotFound($"Review with ID {id} not found.");
            }

            return Ok(review);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }

    [HttpDelete("{id}")]
    [RequirePermission("product_reviews", "update")]
    public async Task<ActionResult<bool>> SoftDelete(Guid id)
    {
        try
        {
            var result = await _productReviewService.SoftDeleteAsync(id);
            if (!result)
            {
                return NotFound($"Review with ID {id} not found or could not be deleted.");
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }

    [HttpPatch("{id}/restore")]
    [RequirePermission("product_reviews", "update")]
    public async Task<ActionResult<bool>> Restore(Guid id)
    {
        try
        {
            var result = await _productReviewService.RestoreAsync(id);
            if (!result)
            {
                return NotFound($"Review with ID {id} not found or could not be restored.");
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }

    [HttpPut("{id}/approve")]
    [RequirePermission("product_reviews", "update")]
    public async Task<ActionResult<bool>> ApproveReview(Guid id)
    {
        try
        {
            var result = await _productReviewService.ApproveReviewAsync(id);
            if (!result)
            {
                return NotFound($"Review with ID {id} not found or could not be approved.");
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }

    [HttpPut("{id}/reject")]
    [RequirePermission("product_reviews", "update")]
    public async Task<ActionResult<bool>> RejectReview(Guid id)
    {
        try
        {
            var result = await _productReviewService.RejectReviewAsync(id);
            if (!result)
            {
                return NotFound($"Review with ID {id} not found or could not be rejected.");
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }
    }
}
