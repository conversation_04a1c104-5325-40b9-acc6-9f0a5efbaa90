namespace PanelApi.DTOs;

/// <summary>
/// Kargo etiketi bilgileri DTO'su
/// </summary>
public class ShippingLabelDto
{
    /// <summary>
    /// Şirket logosu URL'i
    /// </summary>
    public string? CompanyLogoUrl { get; set; }

    /// <summary>
    /// Şirket adı
    /// </summary>
    public string CompanyName { get; set; } = null!;

    /// <summary>
    /// Kargo kodu (barkod olarak gösterilecek)
    /// </summary>
    public string CargoCode { get; set; } = null!;

    /// <summary>
    /// Barkod resmi (Base64 formatında)
    /// </summary>
    public string BarcodeImage { get; set; } = null!;

    /// <summary>
    /// Sipariş numarası
    /// </summary>
    public string OrderNumber { get; set; } = null!;

    /// <summary>
    /// Gönderen bilgileri
    /// </summary>
    public SenderInfoDto Sender { get; set; } = null!;

    /// <summary>
    /// Satı<PERSON> kanalı
    /// </summary>
    public string SalesChannel { get; set; } = null!;

    /// <summary>
    /// Alıcı bilgileri
    /// </summary>
    public RecipientInfoDto Recipient { get; set; } = null!;

    /// <summary>
    /// Kargo şirketi adı
    /// </summary>
    public string CarrierName { get; set; } = null!;

    /// <summary>
    /// Teslimat adresi
    /// </summary>
    public string DeliveryAddress { get; set; } = null!;
}

/// <summary>
/// Gönderen bilgileri
/// </summary>
public class SenderInfoDto
{
    /// <summary>
    /// Gönderen adı
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// Gönderen telefonu
    /// </summary>
    public string? Phone { get; set; }

    /// <summary>
    /// Gönderen adresi
    /// </summary>
    public string? Address { get; set; }
}

/// <summary>
/// Alıcı bilgileri
/// </summary>
public class RecipientInfoDto
{
    /// <summary>
    /// Alıcı adı
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// Alıcı telefonu
    /// </summary>
    public string Phone { get; set; } = null!;

    /// <summary>
    /// Alıcı adresi
    /// </summary>
    public string Address { get; set; } = null!;
}
