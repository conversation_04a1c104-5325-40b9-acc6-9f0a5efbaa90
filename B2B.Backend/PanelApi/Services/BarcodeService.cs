using ZXing;
using ZXing.Common;
using System.Drawing;
using System.Drawing.Imaging;

namespace PanelApi.Services;

/// <summary>
/// Barkod üretimi için servis
/// </summary>
public interface IBarcodeService
{
    /// <summary>
    /// Verilen metni barkoda dönüştürür ve Base64 string olarak döndürür
    /// </summary>
    /// <param name="text">Barkoda dönüştürülecek metin</param>
    /// <param name="width">Barkod genişliği (varsayılan: 300)</param>
    /// <param name="height">Barkod yüksekliği (varsayılan: 100)</param>
    /// <returns>Base64 formatında barkod resmi</returns>
    string GenerateBarcode(string text, int width = 300, int height = 100);
}

/// <summary>
/// Barkod üretimi servis implementasyonu
/// </summary>
public class BarcodeService : IBarcodeService
{
    public string GenerateBarcode(string text, int width = 300, int height = 100)
    {
        try
        {
            var writer = new BarcodeWriter
            {
                Format = BarcodeFormat.CODE_128,
                Options = new EncodingOptions
                {
                    Width = width,
                    Height = height,
                    Margin = 10,
                    PureBarcode = false // Metin de gösterilsin
                }
            };

            using var bitmap = writer.Write(text);
            using var stream = new MemoryStream();
            
            bitmap.Save(stream, ImageFormat.Png);
            var imageBytes = stream.ToArray();
            
            return Convert.ToBase64String(imageBytes);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Barkod oluşturulurken hata oluştu: {ex.Message}", ex);
        }
    }
}
