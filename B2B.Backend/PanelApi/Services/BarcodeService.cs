using ZXing;
using ZXing.Common;
using ZXing.SkiaSharp;
using SkiaSharp;

namespace PanelApi.Services;

/// <summary>
/// Barkod üretimi için servis
/// </summary>
public interface IBarcodeService
{
    /// <summary>
    /// Verilen metni barkoda dönüştürür ve Base64 string olarak döndürür
    /// </summary>
    /// <param name="text">Barkoda dönüştürülecek metin</param>
    /// <param name="width"><PERSON>kod genişliği (varsayılan: 300)</param>
    /// <param name="height"><PERSON>kod yüksekliği (varsayılan: 100)</param>
    /// <returns>Base64 formatında barkod resmi</returns>
    string GenerateBarcode(string text, int width = 300, int height = 300);
}

/// <summary>
/// Barkod üretimi servis implementasyonu
/// </summary>
public class BarcodeService : IBarcodeService
{
    public string GenerateBarcode(string text, int width = 300, int height = 300)
    {
        try
        {
            var writer = new BarcodeWriterPixelData
            {
                Format = BarcodeFormat.CODE_128,
                Options = new EncodingOptions
                {
                    Width = width,
                    Height = height,
                    Margin = 10,
                    PureBarcode = false // Metin de gösterilsin
                }
            };

            var pixelData = writer.Write(text);

            // PixelData'yı SKBitmap'e dönüştür
            var info = new SKImageInfo(pixelData.Width, pixelData.Height, SKColorType.Rgba8888, SKAlphaType.Premul);
            using var bitmap = new SKBitmap(info);

            // Pixel verilerini kopyala
            var pixels = pixelData.Pixels;
            var skPixels = new SKColor[pixels.Length];

            for (int i = 0; i < pixels.Length; i++)
            {
                var pixel = pixels[i];
                skPixels[i] = new SKColor((byte)((pixel >> 16) & 0xFF),
                                         (byte)((pixel >> 8) & 0xFF),
                                         (byte)(pixel & 0xFF),
                                         (byte)((pixel >> 24) & 0xFF));
            }

            bitmap.Pixels = skPixels;

            using var image = SKImage.FromBitmap(bitmap);
            using var data = image.Encode(SKEncodedImageFormat.Png, 100);

            return Convert.ToBase64String(data.ToArray());
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Barkod oluşturulurken hata oluştu: {ex.Message}", ex);
        }
    }
}
