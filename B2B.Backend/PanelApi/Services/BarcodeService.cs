using ZXing;
using ZXing.Common;
using ZXing.SkiaSharp;
using SkiaSharp;

namespace PanelApi.Services;

/// <summary>
/// Barkod üretimi için servis
/// </summary>
public interface IBarcodeService
{
    /// <summary>
    /// Verilen metni barkoda dönüştürür ve Base64 string olarak döndürür
    /// </summary>
    /// <param name="text">Barkoda dönüştürülecek metin</param>
    /// <param name="width">Barkod genişliği (varsayılan: 300)</param>
    /// <param name="height"><PERSON><PERSON>d yüksekliği (varsayılan: 100)</param>
    /// <returns>Base64 formatında barkod resmi</returns>
    string GenerateBarcode(string text, int width = 300, int height = 300);
}

/// <summary>
/// Barkod üretimi servis implementasyonu
/// </summary>
public class BarcodeService : IBarcodeService
{
    public string GenerateBarcode(string text, int width = 300, int height = 300)
    {
        try
        {
            var writer = new BarcodeWriterPixelData
            {
                Format = BarcodeFormat.CODE_128,
                Options = new EncodingOptions
                {
                    Width = width,
                    Height = height,
                    Margin = 10,
                    PureBarcode = false // Metin de gösterilsin
                }
            };

            var pixelData = writer.Write(text);

            // Gerçek boyutları kullan
            var actualWidth = pixelData.Width;
            var actualHeight = pixelData.Height;

            // SKBitmap oluştur
            var info = new SKImageInfo(actualWidth, actualHeight, SKColorType.Rgba8888, SKAlphaType.Premul);
            using var bitmap = new SKBitmap(info);

            // Pixel verilerini doğrudan kopyala
            var pixels = pixelData.Pixels;

            // Pixel array boyutunu kontrol et
            var expectedPixelCount = actualWidth * actualHeight;
            if (pixels.Length != expectedPixelCount)
            {
                throw new InvalidOperationException($"Pixel array boyutu uyuşmuyor. Beklenen: {expectedPixelCount}, Gerçek: {pixels.Length}");
            }

            // Pixel verilerini SKColor array'ine dönüştür
            var skPixels = new SKColor[pixels.Length];
            for (int i = 0; i < pixels.Length; i++)
            {
                var pixel = pixels[i];
                // ARGB formatından SKColor'a dönüştür
                skPixels[i] = new SKColor(
                    (byte)((pixel >> 16) & 0xFF), // R
                    (byte)((pixel >> 8) & 0xFF),  // G
                    (byte)(pixel & 0xFF),         // B
                    (byte)((pixel >> 24) & 0xFF)  // A
                );
            }

            bitmap.Pixels = skPixels;

            using var image = SKImage.FromBitmap(bitmap);
            using var data = image.Encode(SKEncodedImageFormat.Png, 100);

            return Convert.ToBase64String(data.ToArray());
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Barkod oluşturulurken hata oluştu: {ex.Message}", ex);
        }
    }
}
