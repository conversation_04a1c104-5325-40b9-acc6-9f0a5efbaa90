namespace Shipping.Abstraction.Models;

/// <summary>
/// Kargo sorgulama sonucu
/// </summary>
public class ShipmentQueryResult
{
    /// <summary>
    /// İşlem başarılı mı?
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// Hata mesajı (varsa)
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Takip URL'i (kargo firmasından alınan)
    /// </summary>
    public string? TrackingUrl { get; set; }

    /// <summary>
    /// Kargo durumu
    /// </summary>
    public string? Status { get; set; }

    /// <summary>
    /// Durum açıklaması
    /// </summary>
    public string? StatusDescription { get; set; }

    /// <summary>
    /// Kargo işlem gördü mü? (yola çıktı mı?)
    /// </summary>
    public bool IsProcessed { get; set; }

    /// <summary>
    /// Son güncelleme tarihi
    /// </summary>
    public DateTime? LastUpdated { get; set; }

    /// <summary>
    /// Ek bilgiler
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// Detaylı takip bilgileri
    /// </summary>
    public ShipmentTrackingInfo? TrackingInfo { get; set; }
}
