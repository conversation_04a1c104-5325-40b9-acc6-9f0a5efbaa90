using Shipping.Abstraction.Models;

namespace Shipping.Abstraction;

/// <summary>
/// Kargo servisi interface'i
/// Her kargo modülü bu interface'i implement eder
/// </summary>
public interface IShippingService
{
    /// <summary>
    /// Kargo firması tanımı
    /// </summary>
    ShippingCarrierDefinition Definition { get; }

    /// <summary>
    /// Kargo oluştur
    /// </summary>
    /// <param name="request">Kargo isteği</param>
    /// <returns>Takip numarası</returns>
    Task<string> CreateShipmentAsync(ShipmentRequest request);

    /// <summary>
    /// Kargo durumunu sorgula
    /// </summary>
    /// <param name="trackingNumber">Takip numarası</param>
    /// <returns>Takip bilgileri</returns>
    Task<ShipmentTrackingInfo> GetTrackingInfoAsync(string[] trackingNumber);

    /// <summary>
    /// Kargo anahtarı ile kargo durumunu sorgula ve takip anahtarını al
    /// </summary>
    /// <param name="cargoKeys">Kargo anahtarı</param>
    /// <returns>Takip bilgileri ve takip anahtarı</returns>
    Task<ShipmentQueryResult> GetTrackingUrlAsync(string[] cargoKeys);

    /// <summary>
    /// Kargo iptal et
    /// </summary>
    /// <param name="trackingNumber">Takip numarası</param>
    /// <returns>İptal başarılı mı?</returns>
    Task<bool> CancelShipmentAsync(string trackingNumber);

    /// <summary>
    /// Ayarları doğrula
    /// </summary>
    /// <param name="settings">Ayarlar</param>
    /// <returns>Ayarlar geçerli mi?</returns>
    Task<bool> ValidateSettingsAsync(Dictionary<string, string> settings);

    /// <summary>
    /// Kargo maliyeti hesapla (opsiyonel)
    /// </summary>
    /// <param name="request">Kargo isteği</param>
    /// <returns>Maliyet bilgisi</returns>
    Task<decimal?> CalculateShippingCostAsync(ShipmentRequest request);
}

/// <summary>
/// Kargo oluşturma isteği
/// </summary>
public class ShipmentRequest
{
    /// <summary>
    /// Sipariş ID
    /// </summary>
    public Guid OrderId { get; set; }

    /// <summary>
    /// Alıcı adı soyadı
    /// </summary>
    public string RecipientName { get; set; } = null!;

    /// <summary>
    /// Alıcı telefon numarası
    /// </summary>
    public string RecipientPhone { get; set; } = null!;

    /// <summary>
    /// Alıcı e-posta adresi (opsiyonel)
    /// </summary>
    public string? RecipientEmail { get; set; }

    /// <summary>
    /// Teslimat adresi
    /// </summary>
    public string Address { get; set; } = null!;

    /// <summary>
    /// İl
    /// </summary>
    public string City { get; set; } = null!;

    /// <summary>
    /// İlçe
    /// </summary>
    public string District { get; set; } = null!;

    /// <summary>
    /// Posta kodu
    /// </summary>
    public string PostalCode { get; set; } = null!;

    /// <summary>
    /// Ülke (varsayılan: TR)
    /// </summary>
    public string Country { get; set; } = "TR";

    /// <summary>
    /// Ağırlık (kg)
    /// </summary>
    public decimal Weight { get; set; }

    /// <summary>
    /// Boyutlar (cm)
    /// </summary>
    public ShipmentDimensions? Dimensions { get; set; }

    /// <summary>
    /// Kargo değeri (sigorta için)
    /// </summary>
    public decimal? DeclaredValue { get; set; }

    /// <summary>
    /// Özel talimatlar
    /// </summary>
    public string? SpecialInstructions { get; set; }

    /// <summary>
    /// Ürün açıklaması
    /// </summary>
    public string? ProductDescription { get; set; }

    /// <summary>
    /// Gönderici bilgileri (opsiyonel, varsayılan şirket bilgileri kullanılır)
    /// </summary>
    public SenderInfo? Sender { get; set; }
}

/// <summary>
/// Kargo boyutları
/// </summary>
public class ShipmentDimensions
{
    /// <summary>
    /// Uzunluk (cm)
    /// </summary>
    public decimal Length { get; set; }

    /// <summary>
    /// Genişlik (cm)
    /// </summary>
    public decimal Width { get; set; }

    /// <summary>
    /// Yükseklik (cm)
    /// </summary>
    public decimal Height { get; set; }
}

/// <summary>
/// Gönderici bilgileri
/// </summary>
public class SenderInfo
{
    /// <summary>
    /// Gönderici adı
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// Gönderici telefonu
    /// </summary>
    public string Phone { get; set; } = null!;

    /// <summary>
    /// Gönderici adresi
    /// </summary>
    public string Address { get; set; } = null!;

    /// <summary>
    /// Gönderici ili
    /// </summary>
    public string City { get; set; } = null!;

    /// <summary>
    /// Gönderici ilçesi
    /// </summary>
    public string District { get; set; } = null!;

    /// <summary>
    /// Gönderici posta kodu
    /// </summary>
    public string PostalCode { get; set; } = null!;
}
