using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Shipping.Abstraction;
using Shipping.Abstraction.Models;
using Shipping.YurticiKargo.Models;
using Shipping.YurticiKargo.KOPSWebServices;

namespace Shipping.YurticiKargo;

/// <summary>
/// Yurtiçi Kargo shipping service implementasyonu
/// </summary>
public class YurticiShippingService : IShippingService
{
    private readonly ILogger<YurticiShippingService>? _logger;
    private readonly IConfiguration _configuration;
    private readonly YurticiCarrierDefinition _definition;
    private readonly YurticiApiSettings _apiSettings;

    public YurticiShippingService(
        ILogger<YurticiShippingService>? logger,
        IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
        _definition = new YurticiCarrierDefinition();
        _apiSettings = LoadApiSettings();
    }

    public ShippingCarrierDefinition Definition => _definition;

    public async Task<string> CreateShipmentAsync(ShipmentRequest request)
    {
        _logger?.LogInformation("Creating Yurtiçi Kargo shipment for Order: {OrderId}", request.OrderId);

        try
        {
            ValidateShipmentRequest(request);
            var yurticiRequest = MapToYurticiRequest(request);
            var response = await CallCreateShipmentApiAsync(yurticiRequest);

            if (response.IsSuccess)
            {
                _logger?.LogInformation("Yurtiçi Kargo shipment created successfully. Tracking: {TrackingNumber}", response.CargoKey);
                return response.CargoKey ?? throw new InvalidOperationException("API'den geçerli kargo anahtarı alınamadı");
            }
            else
            {
                var errorMessage = $"Yurtiçi Kargo API Error: {response.ErrorCode} - {response.ErrorMessage}";
                _logger?.LogError(errorMessage);
                throw new InvalidOperationException(errorMessage);
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error creating Yurtiçi Kargo shipment for Order: {OrderId}", request.OrderId);
            throw;
        }
    }

    public async Task<ShipmentTrackingInfo> GetTrackingInfoAsync(string[] trackingNumber)
    {
        _logger?.LogInformation("Getting Yurtiçi Kargo tracking info for: {TrackingNumber}", trackingNumber);

        try
        {
            if (trackingNumber == null || trackingNumber.Length == 0)
                throw new ArgumentException("Takip numarası boş olamaz", nameof(trackingNumber));

            var response = await QueryShipmentAsync(new YurticiQueryShipmentRequest
            {
                Key = trackingNumber,
                KeyType = 1,
                OnlyTracking = false,
                AddHistoricalData = false
            });

            if (response.IsSuccess)
            {
                var trackingInfo = MapToTrackingInfo(response, trackingNumber);
                _logger?.LogInformation("Yurtiçi Kargo tracking info retrieved successfully for: {TrackingNumber}", trackingNumber);
                return trackingInfo;
            }
            else
            {
                var errorMessage = $"Yurtiçi Kargo API Error: {response.ErrorCode} - {response.ErrorMessage}";
                _logger?.LogError(errorMessage);
                throw new InvalidOperationException(errorMessage);
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error getting Yurtiçi Kargo tracking info for: {TrackingNumber}", trackingNumber);
            throw;
        }
    }

    public async Task<ShipmentQueryResult> GetTrackingUrlAsync(string[] cargoKeys)
    {
        _logger?.LogInformation("Getting Yurtiçi Kargo tracking URL for CargoKey: {CargoKey}", cargoKeys.ToString());

        try
        {
            if (cargoKeys == null || cargoKeys.Length == 0)
                throw new ArgumentException("Kargo anahtarı boş olamaz", nameof(cargoKeys));

            var response = await QueryShipmentAsync(new YurticiQueryShipmentRequest
            {
                Key = cargoKeys,
                KeyType = 0,
                OnlyTracking = true,
                AddHistoricalData = true
            });

            var result = new ShipmentQueryResult
            {
                IsSuccess = response.IsSuccess,
                ErrorMessage = response.ErrorMessage
            };

            if (response.IsSuccess && response.ShipmentInfo != null)
            {
                result.TrackingUrl = response.ShipmentInfo.TrackingUrl;
                result.Status = response.ShipmentInfo.Status;
                result.StatusDescription = response.ShipmentInfo.StatusDescription;
                result.LastUpdated = response.ShipmentInfo.LastUpdateDate;
                result.Notes = response.ShipmentInfo.Notes;
                result.IsProcessed = IsShipmentProcessed(response.ShipmentInfo.Status);
                result.TrackingInfo = MapToTrackingInfo(response, cargoKeys);

                _logger?.LogInformation("Successfully got tracking URL for CargoKey {CargoKey}. TrackingUrl: {TrackingUrl}, Status: {Status}",
                    cargoKeys, result.TrackingUrl, result.Status);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error getting Yurtiçi Kargo tracking URL for CargoKey: {CargoKey}", cargoKeys);
            return new ShipmentQueryResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task<bool> CancelShipmentAsync(string trackingNumber)
    {
        _logger?.LogInformation("Cancelling Yurtiçi Kargo shipment: {TrackingNumber}", trackingNumber);

        try
        {
            if (string.IsNullOrWhiteSpace(trackingNumber))
                throw new ArgumentException("Takip numarası boş olamaz", nameof(trackingNumber));

            // Yurtiçi Kargo API'sinde iptal işlemi için özel endpoint kullanılır
            _logger?.LogWarning("Yurtiçi Kargo cancel shipment API not implemented yet for: {TrackingNumber}", trackingNumber);
            await Task.Delay(100); // Simüle edilmiş gecikme
            return false;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error cancelling Yurtiçi Kargo shipment: {TrackingNumber}", trackingNumber);
            return false;
        }
    }

    public async Task<bool> ValidateSettingsAsync(Dictionary<string, string> settings)
    {
        try
        {
            if (!settings.ContainsKey("WsUserName") || string.IsNullOrWhiteSpace(settings["WsUserName"]))
                return false;

            if (!settings.ContainsKey("WsPassword") || string.IsNullOrWhiteSpace(settings["WsPassword"]))
                return false;

            // Basit bir test sorgusu yap
            var response = await QueryShipmentAsync(new YurticiQueryShipmentRequest
            {
                Key = new[] { "TEST123" },
                KeyType = 1,
                OnlyTracking = true,
                AddHistoricalData = false
            });
            return response.ErrorCode != "EXCEPTION";
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error validating Yurtiçi Kargo settings");
            return false;
        }
    }

    public Task<decimal?> CalculateShippingCostAsync(ShipmentRequest request)
    {
        _logger?.LogInformation("Calculating Yurtiçi Kargo shipping cost for Order: {OrderId}", request.OrderId);

        try
        {
            decimal baseCost = 15.00m;
            decimal weightCost = request.Weight * 2.50m;
            decimal cityCost = GetCityCost(request.City);
            decimal totalCost = baseCost + weightCost + cityCost;

            if (totalCost < 10.00m)
                totalCost = 10.00m;

            _logger?.LogInformation("Yurtiçi Kargo shipping cost calculated: {Cost} for Order: {OrderId}", totalCost, request.OrderId);
            return Task.FromResult<decimal?>(totalCost);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error calculating Yurtiçi Kargo shipping cost for Order: {OrderId}", request.OrderId);
            return Task.FromResult<decimal?>(null);
        }
    }

    #region Private Methods

    private YurticiApiSettings LoadApiSettings()
    {
        return new YurticiApiSettings
        {
            WsUserName = _configuration["YURTICI_WS_USERNAME"] ??
                        _configuration["Shipping:Yurtici:WsUserName"] ??
                        throw new InvalidOperationException("Yurtiçi Kargo WsUserName ayarı bulunamadı"),

            WsPassword = _configuration["YURTICI_WS_PASSWORD"] ??
                        _configuration["Shipping:Yurtici:WsPassword"] ??
                        throw new InvalidOperationException("Yurtiçi Kargo WsPassword ayarı bulunamadı"),

            WsLanguage = _configuration["YURTICI_WS_LANGUAGE"] ??
                        _configuration["Shipping:Yurtici:WsLanguage"] ?? "TR",

            ApiUrl = _configuration["YURTICI_API_URL"] ??
                    _configuration["Shipping:Yurtici:ApiUrl"] ??
                    "https://api.yurtici.com.tr",

            TestMode = false,
            Timeout = int.Parse(_configuration["YURTICI_TIMEOUT"] ??
                               _configuration["Shipping:Yurtici:Timeout"] ?? "30"),
            MaxRetries = int.Parse(_configuration["YURTICI_MAX_RETRIES"] ??
                                  _configuration["Shipping:Yurtici:MaxRetries"] ?? "3")
        };
    }

    private static void ValidateShipmentRequest(ShipmentRequest request)
    {
        if (request == null)
            throw new ArgumentNullException(nameof(request));
        if (string.IsNullOrWhiteSpace(request.RecipientName))
            throw new ArgumentException("Alıcı adı boş olamaz", nameof(request.RecipientName));
        if (string.IsNullOrWhiteSpace(request.RecipientPhone))
            throw new ArgumentException("Alıcı telefonu boş olamaz", nameof(request.RecipientPhone));
        if (string.IsNullOrWhiteSpace(request.Address))
            throw new ArgumentException("Adres boş olamaz", nameof(request.Address));
        if (string.IsNullOrWhiteSpace(request.City))
            throw new ArgumentException("Şehir boş olamaz", nameof(request.City));
        if (request.Weight <= 0)
            throw new ArgumentException("Ağırlık 0'dan büyük olmalıdır", nameof(request.Weight));
    }

    private static YurticiCreateShipmentRequest MapToYurticiRequest(ShipmentRequest request)
    {
        var cargoKey = $"YK{DateTime.Now:yyyyMMddHHmmss}{Random.Shared.Next(1000, 9999)}";
        var invoiceKey = $"INV{request.OrderId.ToString("N")[..8].ToUpper()}";
        var phone = request.RecipientPhone.Trim();
        if (!phone.StartsWith("0"))
            phone = "0" + phone;

        return new YurticiCreateShipmentRequest
        {
            CargoKey = cargoKey,
            InvoiceKey = invoiceKey,
            ReceiverCustName = request.RecipientName.Trim(),
            ReceiverAddress = $"{request.Address}, {request.District}/{request.City}".Trim(),
            ReceiverPhone1 = phone,
            ReceiverEmail = request.RecipientEmail,
            DeclaredValue = request.DeclaredValue,
            Weight = request.Weight,
            SpecialInstructions = request.SpecialInstructions
        };
    }

    private static ShipmentTrackingInfo MapToTrackingInfo(YurticiQueryShipmentResponse response, string[] trackingNumber)
    {
        return new ShipmentTrackingInfo
        {
            TrackingNumber = response.TrackingUrl,
            CargoKey = trackingNumber[0],
            Status = MapYurticiStatusToStandard(response.Status),
            StatusDescription = response.StatusDescription ?? "Durum bilgisi alınamadı",
            LastUpdate = response.LastUpdate ?? DateTime.UtcNow,
            EstimatedDelivery = response.EstimatedDelivery,
            CurrentLocation = response.CurrentLocation ?? "Bilinmiyor",
            TrackingEvents = new List<TrackingEvent>
            {
                new()
                {
                    EventDate = response.LastUpdate ?? DateTime.UtcNow,
                    Description = response.StatusDescription ?? "Durum güncellendi",
                    Location = response.CurrentLocation ?? "Yurtiçi Kargo"
                }
            }
        };
    }

    private static string MapYurticiStatusToStandard(string? yurticiStatus)
    {
        return yurticiStatus?.ToLowerInvariant() switch
        {
            "alindi" or "kargo_alindi" => "Picked",
            "yolda" or "transfer_merkezi" => "InTransit",
            "dagitimda" or "dagitim_merkezi" => "OutForDelivery",
            "teslim_edildi" or "teslim" => "Delivered",
            "iptal" or "iptal_edildi" => "Cancelled",
            "iade" or "iade_edildi" => "Returned",
            _ => "InTransit"
        };
    }

    private static decimal GetCityCost(string city)
    {
        var majorCities = new[] { "İSTANBUL", "ANKARA", "İZMİR", "BURSA", "ANTALYA" };
        return majorCities.Contains(city.ToUpperInvariant()) ? 0m : 5.00m;
    }

    private static bool IsShipmentProcessed(string? status)
    {
        if (string.IsNullOrEmpty(status))
            return false;

        var processedStatuses = new[]
        {
            "YOLDA", "DAGITIMDA", "TESLIM_EDILDI", "TRANSIT", "IN_TRANSIT",
            "OUT_FOR_DELIVERY", "DELIVERED", "YOLA_CIKTI", "SEVK_EDILDI"
        };

        return processedStatuses.Any(s => status.Contains(s, StringComparison.OrdinalIgnoreCase));
    }

    private async Task<YurticiCreateShipmentResponse> CallCreateShipmentApiAsync(YurticiCreateShipmentRequest request)
    {
        try
        {
            using var client = new KOPSWebServices.ShippingOrderDispatcherServicesClient();

            if (client.Endpoint.Binding is System.ServiceModel.BasicHttpBinding binding)
            {
                binding.SendTimeout = TimeSpan.FromSeconds(_apiSettings.Timeout);
                binding.ReceiveTimeout = TimeSpan.FromSeconds(_apiSettings.Timeout);
                binding.OpenTimeout = TimeSpan.FromSeconds(30);
                binding.CloseTimeout = TimeSpan.FromSeconds(30);
            }

            var createShipmentRequest = new KOPSWebServices.createShipment
            {
                wsUserName = _apiSettings.WsUserName,
                wsPassword = _apiSettings.WsPassword,
                userLanguage = _apiSettings.WsLanguage,
                ShippingOrderVO = new KOPSWebServices.ShippingOrderVO[]
                {
                    new KOPSWebServices.ShippingOrderVO
                    {
                        cargoKey = request.CargoKey,
                        invoiceKey = request.InvoiceKey,
                        receiverCustName = request.ReceiverCustName,
                        receiverAddress = request.ReceiverAddress,
                        receiverPhone1 = request.ReceiverPhone1
                    }
                }
            };

            var response = await client.createShipmentAsync(createShipmentRequest);
            return ParseCreateShipmentResponse(response);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error calling Yurtiçi Kargo createShipment API");
            return new YurticiCreateShipmentResponse
            {
                ErrorCode = "EXCEPTION",
                ErrorMessage = ex.Message
            };
        }
    }

    private async Task<YurticiQueryShipmentResponse> QueryShipmentAsync(YurticiQueryShipmentRequest request)
    {
        try
        {
            if (request.Key == null || request.Key.Length == 0)
                throw new ArgumentException("Anahtar boş olamaz", nameof(request.Key));

            using var client = new KOPSWebServices.ShippingOrderDispatcherServicesClient();

            if (client.Endpoint.Binding is System.ServiceModel.BasicHttpBinding binding)
            {
                binding.SendTimeout = TimeSpan.FromSeconds(_apiSettings.Timeout);
                binding.ReceiveTimeout = TimeSpan.FromSeconds(_apiSettings.Timeout);
                binding.OpenTimeout = TimeSpan.FromSeconds(30);
                binding.CloseTimeout = TimeSpan.FromSeconds(30);
            }

            var queryShipmentRequest = new KOPSWebServices.queryShipment
            {
                wsUserName = _apiSettings.WsUserName,
                wsPassword = _apiSettings.WsPassword,
                wsLanguage = _apiSettings.WsLanguage,
                keyType = request.KeyType,
                keys = request.Key,
                onlyTracking = request.OnlyTracking,
                addHistoricalData = request.AddHistoricalData
            };

            var response = await client.queryShipmentAsync(queryShipmentRequest);
            return ParseQueryShipmentResponse(response);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error calling Yurtiçi Kargo queryShipment API");
            return new YurticiQueryShipmentResponse
            {
                ErrorCode = "EXCEPTION",
                ErrorMessage = ex.Message
            };
        }
    }

    private YurticiCreateShipmentResponse ParseCreateShipmentResponse(KOPSWebServices.createShipmentResponse1 soapResponse)
    {
        try
        {
            var response = new YurticiCreateShipmentResponse();

            if (soapResponse?.createShipmentResponse?.ShippingOrderResultVO?.shippingOrderDetailVO != null)
            {
                var orderDetail = soapResponse.createShipmentResponse.ShippingOrderResultVO.shippingOrderDetailVO.FirstOrDefault();

                if (orderDetail != null)
                {
                    response.CargoKey = orderDetail.cargoKey;
                    response.InvoiceKey = orderDetail.invoiceKey;
                    response.ErrorCode = orderDetail.errCode.ToString();
                    response.ErrorMessage = orderDetail.errMessage;
                    response.OperationMessage = string.IsNullOrEmpty(orderDetail.errMessage) ? "Success" : orderDetail.errMessage;
                    response.OperationStatus = orderDetail.errCode == 0 ? "Success" : "Error";
                }
            }

            return response;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error parsing Yurtiçi Kargo createShipment response");
            return new YurticiCreateShipmentResponse
            {
                ErrorCode = "PARSE_ERROR",
                ErrorMessage = $"Response parsing error: {ex.Message}"
            };
        }
    }

    private YurticiQueryShipmentResponse ParseQueryShipmentResponse(KOPSWebServices.queryShipmentResponse1 soapResponse)
    {
        try
        {
            var response = new YurticiQueryShipmentResponse();

            if (soapResponse?.queryShipmentResponse?.ShippingDeliveryVO?.shippingDeliveryDetailVO != null)
            {
                var deliveryDetail = soapResponse.queryShipmentResponse.ShippingDeliveryVO.shippingDeliveryDetailVO.FirstOrDefault();

                if (deliveryDetail != null)
                {
                    response.ErrorCode = deliveryDetail.errCode.ToString();
                    response.ErrorMessage = deliveryDetail.errMessage;
                    response.InvoiceKey = deliveryDetail.invoiceKey;
                    response.OperationMessage = deliveryDetail.operationMessage;
                    response.OperationStatus = deliveryDetail.operationStatus;

                    if (deliveryDetail.shippingDeliveryItemDetailVO != null)
                    {
                        var itemDetail = deliveryDetail.shippingDeliveryItemDetailVO;
                        response.CargoKey = itemDetail.docId;
                        response.TrackingUrl = itemDetail.trackingUrl;

                        var statusText = itemDetail.cargoEventExplanation ?? itemDetail.deliveryTypeExplanation ?? "Kargo işlemde";
                        response.Status = MapYurticiStatusToStandard(statusText);
                        response.StatusDescription = statusText;

                        if (DateTime.TryParse(itemDetail.deliveryDate, out var deliveryDate))
                            response.LastUpdate = deliveryDate;
                        else
                            response.LastUpdate = DateTime.UtcNow;

                        response.CurrentLocation = itemDetail.deliveryUnitName ?? itemDetail.arrivalUnitName ?? "Yurtiçi Kargo";

                        response.ShipmentInfo = new YurticiShipmentInfo
                        {
                            TrackingCode = itemDetail.trackingUrl?.Split('=').LastOrDefault(),
                            TrackingUrl = itemDetail.trackingUrl,
                            Status = response.Status,
                            StatusDescription = response.StatusDescription,
                            LastUpdateDate = response.LastUpdate,
                            CurrentLocation = response.CurrentLocation,
                            Notes = $"{itemDetail.cargoEventExplanation} - {itemDetail.deliveryTypeExplanation}".Trim(' ', '-')
                        };
                    }
                }
            }

            if (string.IsNullOrEmpty(response.Status))
            {
                response.Status = "InTransit";
                response.StatusDescription = "Kargo takip edilebilir durumda";
                response.LastUpdate = DateTime.UtcNow;
                response.CurrentLocation = "Yurtiçi Kargo";

                response.ShipmentInfo = new YurticiShipmentInfo
                {
                    Status = response.Status,
                    StatusDescription = response.StatusDescription,
                    LastUpdateDate = response.LastUpdate,
                    CurrentLocation = response.CurrentLocation
                };
            }

            return response;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error parsing Yurtiçi Kargo queryShipment response");
            return new YurticiQueryShipmentResponse
            {
                ErrorCode = "PARSE_ERROR",
                ErrorMessage = $"Response parsing error: {ex.Message}"
            };
        }
    }

    #endregion
}
